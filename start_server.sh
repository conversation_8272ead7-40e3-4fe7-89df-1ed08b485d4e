#!/bin/bash

echo "========================================"
echo "MCP Web Scraper Service"
echo "========================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.12 or later"
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Warning: .env file not found"
    echo "Please copy .env.example to .env and configure your API keys"
    echo
fi

# Start the server
echo "Starting MCP Web Scraper Service..."
echo "Press Ctrl+C to stop the server"
echo
python3 start_server.py
