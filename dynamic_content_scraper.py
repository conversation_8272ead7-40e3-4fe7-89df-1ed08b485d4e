import os
import asyncio
import argparse
import json
from datetime import datetime
from playwright.async_api import async_playwright


class DynamicContentScraper:
    def __init__(self, output_dir="scraped_data"):
        """
        初始化爬虫类，专门用于处理动态加载内容
        
        Args:
            output_dir: 保存爬取内容的目录
        """
        self.output_dir = output_dir
        self.browser = None
        self.context = None
        self.page = None
        self._setup_directories()
    
    def _setup_directories(self):
        """创建保存爬取内容的目录"""
        # 创建主输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 创建子目录
        self.images_dir = os.path.join(self.output_dir, "images")
        self.data_dir = os.path.join(self.output_dir, "data")
        
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    async def _setup_browser(self):
        """配置并启动无头浏览器"""
        self.playwright = await async_playwright().start()
        
        # 配置浏览器选项
        browser_config = {
            "headless": True,
            "slow_mo": 50  # 减慢操作速度，对某些动态网站很有用
        }
        
        self.browser = await self.playwright.chromium.launch(**browser_config)
        
        # 配置上下文选项
        context_config = {
            "viewport": {"width": 1920, "height": 1080},
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            # 添加语言和地区设置
            "locale": "zh-CN"
        }
        
        self.context = await self.browser.new_context(**context_config)
        
        # 启用网络请求监控
        await self.context.route("**/*", lambda route: route.continue_())
        
        # 创建新页面
        self.page = await self.context.new_page()
        
        # 设置页面加载超时时间（30秒）
        self.page.set_default_timeout(30000)
    
    async def scrape_dynamic_content(self, url, selectors=None, actions=None, wait_time=5000):
        """
        爬取动态加载的内容
        
        Args:
            url: 要爬取的URL
            selectors: 要提取内容的CSS选择器字典，格式为 {"name": "selector"}
            actions: 要执行的操作列表，例如点击、滚动等
            wait_time: 等待时间（毫秒）
        """
        try:
            if not self.browser:
                await self._setup_browser()
            
            # 如果没有提供选择器，使用默认值
            if selectors is None:
                selectors = {
                    "title": "title",
                    "main_content": "main",
                    "headings": "h1, h2, h3",
                    "paragraphs": "p",
                    "links": "a"
                }
            
            # 如果没有提供操作，使用空列表
            if actions is None:
                actions = []
            
            print(f"开始爬取动态内容: {url}")
            
            # 导航到URL
            response = await self.page.goto(url, wait_until="networkidle")
            
            # 检查响应状态
            if response.status >= 400:
                print(f"页面请求失败: {response.status} {response.status_text}")
                return False
            
            # 等待一段时间，确保JavaScript加载完成
            await self.page.wait_for_timeout(wait_time)
            
            # 执行自定义操作
            for action in actions:
                action_type = action.get("type")
                if action_type == "click":
                    await self.page.click(action.get("selector"))
                    await self.page.wait_for_timeout(action.get("wait", 1000))
                elif action_type == "scroll":
                    await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                    await self.page.wait_for_timeout(action.get("wait", 1000))
                elif action_type == "wait_for_selector":
                    await self.page.wait_for_selector(action.get("selector"), state="visible")
                elif action_type == "type":
                    await self.page.fill(action.get("selector"), action.get("text", ""))
                elif action_type == "eval":
                    await self.page.evaluate(action.get("script"))
            
            # 保存截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = os.path.join(self.images_dir, f"{timestamp}_screenshot.png")
            await self.page.screenshot(path=screenshot_path, full_page=True)
            print(f"已保存页面截图: {screenshot_path}")
            
            # 提取内容
            extracted_data = {}
            for name, selector in selectors.items():
                try:
                    # 对于单个元素
                    if name.endswith("_single"):
                        element = await self.page.query_selector(selector)
                        if element:
                            text = await element.text_content()
                            extracted_data[name] = text.strip()
                    # 对于多个元素
                    else:
                        elements = await self.page.query_selector_all(selector)
                        extracted_data[name] = []
                        for element in elements:
                            text = await element.text_content()
                            href = await element.get_attribute("href") if name == "links" else None
                            item = {"text": text.strip()}
                            if href:
                                item["href"] = href
                            extracted_data[name].append(item)
                except Exception as e:
                    print(f"提取 {name} 内容时出错: {str(e)}")
            
            # 保存提取的数据
            data_path = os.path.join(self.data_dir, f"{timestamp}_data.json")
            with open(data_path, "w", encoding="utf-8") as f:
                json.dump(extracted_data, f, ensure_ascii=False, indent=2)
            print(f"已保存提取的数据: {data_path}")
            
            # 可选：保存页面HTML源码
            html_path = os.path.join(self.data_dir, f"{timestamp}_page.html")
            html_content = await self.page.content()
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"已保存页面HTML: {html_path}")
            
            print(f"成功爬取动态内容: {url}")
            return True
        
        except Exception as e:
            print(f"爬取动态内容时出错: {str(e)}")
            return False
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        
        if hasattr(self, "playwright"):
            await self.playwright.stop()


async def main_async():
    parser = argparse.ArgumentParser(description="动态内容爬取工具")
    parser.add_argument("url", help="要爬取的网页URL")
    parser.add_argument("--output", default="scraped_data", help="输出目录")
    parser.add_argument("--wait", type=int, default=5000, help="页面加载等待时间（毫秒）")
    parser.add_argument("--config", help="配置文件路径，JSON格式，包含selectors和actions")
    
    args = parser.parse_args()
    
    # 读取配置文件
    selectors = None
    actions = None
    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, "r", encoding="utf-8") as f:
                config = json.load(f)
                selectors = config.get("selectors")
                actions = config.get("actions")
        except Exception as e:
            print(f"读取配置文件时出错: {str(e)}")
    
    scraper = DynamicContentScraper(output_dir=args.output)
    try:
        await scraper.scrape_dynamic_content(
            args.url,
            selectors=selectors,
            actions=actions,
            wait_time=args.wait
        )
    finally:
        await scraper.close()


def main():
    """命令行入口点"""
    asyncio.run(main_async())


if __name__ == "__main__":
    main() 