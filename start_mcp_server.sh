#!/bin/bash

# Start the MCP server with SSE transport
# Usage: ./start_mcp_server.sh [host] [port]

# Default values
HOST=${1:-127.0.0.1}
PORT=${2:-8000}

echo "Starting MCP Scraper Service with SSE transport..."
echo "Host: $HOST"
echo "Port: $PORT"
echo "URL: http://$HOST:$PORT/sse"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Run the MCP server using fastmcp CLI
fastmcp run mcp_scraper_service.py --transport sse --host $HOST --port $PORT 