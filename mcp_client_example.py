import asyncio
from fastmcp import Client
import json

async def main():
    # Connect to the MCP server using SSE transport
    # The URL format is: http://{host}:{port}/sse
    async with <PERSON><PERSON>("http://127.0.0.1:8000/sse") as client:
        print("Connected to MCP server via SSE")
        
        # List available tools
        tools = await client.list_tools()
        print(f"Available tools: {[tool.name for tool in tools]}")
        
        # List available resources
        resources = await client.list_resources()
        print(f"Available resources: {[resource.uri_template for resource in resources]}")
        
        # List available prompts
        prompts = await client.list_prompts()
        print(f"Available prompts: {[prompt.name for prompt in prompts]}")
        
        # Example URL to scrape
        url = "https://example.com"
        
        print(f"\nScraping URL with progress: {url}")
        # Call the scrape_url_with_progress tool
        result = await client.call_tool("scrape_url_with_progress", {"url": url})
        
        # Print the result
        if result:
            print("\nScraping result:")
            for content in result:
                if hasattr(content, 'text'):
                    # Parse the JSON result
                    try:
                        data = json.loads(content.text)
                        print(f"Success: {data['success']}")
                        print(f"URL: {data['url']}")
                        print(f"Text content preview: {data['text_content'][:100]}...")
                        print(f"Image count: {data['image_count']}")
                    except json.JSONDecodeError:
                        print(f"Raw result: {content.text}")
                else:
                    print(f"Non-text content: {content}")
        
        # Read the scraped content resource
        print("\nReading scraped content resource:")
        content, mime_type = await client.read_resource(f"scraped-content://{url}")
        print(f"Content type: {mime_type}")
        print(f"Content preview: {content[:100]}...")
        
        # Get a prompt for analysis
        print("\nGetting analysis prompt:")
        prompt_result = await client.get_prompt("scrape_request", {"url": url})
        for message in prompt_result.messages:
            print(f"Prompt: {message.content.text}")

if __name__ == "__main__":
    asyncio.run(main()) 