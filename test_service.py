#!/usr/bin/env python3
"""
Test script for the MCP Web Scraper Service
"""

import asyncio
import json
from mcp_scraper_service import scrape_url_analyze_content, config

async def test_scraping():
    """Test the scraping functionality"""
    print("Testing MCP Web Scraper Service")
    print("=" * 50)
    
    # Test URL
    test_url = "https://httpbin.org/html"
    
    print(f"Testing URL: {test_url}")
    print(f"Output directory: {config.output_dir}")
    print(f"Doubao API available: {'Yes' if config.doubao_api_key else 'No'}")
    print()
    
    try:
        # Call the scraping function
        result = await scrape_url_analyze_content(
            url=test_url,
            wait_time=5000,
            custom_prompt="请简要描述这张图片的内容"
        )
        
        print("Result:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("\n✅ Test completed successfully!")
            print(f"📄 Text content length: {len(result.get('text_content', ''))}")
            print(f"🖼️  Images found: {result.get('image_count', 0)}")
        else:
            print(f"\n❌ Test failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_scraping())
