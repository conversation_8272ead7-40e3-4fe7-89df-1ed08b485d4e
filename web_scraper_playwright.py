import os
import asyncio
import argparse
import requests
from PIL import Image
from io import BytesIO
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright


class PlaywrightScraper:
    def __init__(self, output_dir="scraped_data"):
        """
        初始化爬虫类
        
        Args:
            output_dir: 保存爬取内容的目录
        """
        self.output_dir = output_dir
        self.browser = None
        self.context = None
        self.page = None
        self._setup_directories()
    
    def _setup_directories(self):
        """创建保存爬取内容的目录"""
        # 创建主输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 创建图片和文本子目录
        self.images_dir = os.path.join(self.output_dir, "images")
        self.text_dir = os.path.join(self.output_dir, "text")
        
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)
        if not os.path.exists(self.text_dir):
            os.makedirs(self.text_dir)
    
    async def _setup_browser(self):
        """配置并启动无头浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=True)
        self.context = await self.browser.new_context(viewport={"width": 1920, "height": 1080})
        self.page = await self.context.new_page()
        
        # 配置页面以允许下载图片
        await self.page.route("**/*", lambda route: route.continue_())
    
    async def scrape_url(self, url, wait_time=5000):
        """
        爬取指定URL的内容
        
        Args:
            url: 要爬取的URL
            wait_time: 等待页面加载的时间（毫秒）
        """
        try:
            if not self.browser:
                await self._setup_browser()
            
            print(f"开始爬取: {url}")
            
            # 导航到URL
            await self.page.goto(url, wait_until="networkidle")
            
            # 额外等待一段时间，确保动态内容加载完成
            if wait_time > 0:
                await self.page.wait_for_timeout(wait_time)
            
            # 保存页面截图
            await self._save_screenshot(url)
            
            # 获取并保存文本内容
            await self._extract_and_save_text(url)
            
            # 获取并保存图片
            await self._extract_and_save_images(url)
            
            print(f"成功爬取: {url}")
            return True
        
        except Exception as e:
            print(f"爬取 {url} 时出错: {str(e)}")
            return False
    
    async def _save_screenshot(self, url):
        """保存整个页面的截图"""
        filename = self._get_filename_from_url(url) + "_screenshot.png"
        filepath = os.path.join(self.images_dir, filename)
        
        # 获取整个页面的尺寸
        page_dimensions = await self.page.evaluate("""() => {
            return {
                width: document.documentElement.scrollWidth,
                height: document.documentElement.scrollHeight
            }
        }""")
        
        # 设置视口大小为页面大小
        await self.page.set_viewport_size({
            'width': page_dimensions['width'],
            'height': page_dimensions['height']
        })
        
        # 截取整个页面
        await self.page.screenshot(path=filepath, full_page=True)
        print(f"已保存页面截图: {filepath}")
    
    async def _extract_and_save_text(self, url):
        """提取并保存页面文本内容"""
        # 获取页面HTML
        html = await self.page.content()
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除script和style元素
        for script in soup(["script", "style"]):
            script.extract()
        
        # 获取文本
        text = soup.get_text(separator='\n')
        
        # 清理文本（移除多余空行等）
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        # 保存文本
        filename = self._get_filename_from_url(url) + ".txt"
        filepath = os.path.join(self.text_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(text)
        
        print(f"已保存文本内容: {filepath}")
    
    async def _extract_and_save_images(self, url):
        """提取并保存页面上的webp图片"""
        # 获取所有图片元素的src属性
        img_srcs = await self.page.evaluate("""() => {
            const images = Array.from(document.querySelectorAll('img'));
            return images.map(img => {
                return {
                    src: img.src,
                    alt: img.alt,
                    width: img.width,
                    height: img.height
                };
            }).filter(img => img.src && !img.src.startsWith('data:') && img.width > 10 && img.height > 10);
        }""")
        
        base_filename = self._get_filename_from_url(url)
        saved_count = 0
        
        for i, img_data in enumerate(img_srcs):
            try:
                img_url = img_data['src']
                
                # 只下载webp类型的图片 - 严格检查
                if not img_url.lower().endswith('.webp'):
                    # 检查Content-Type
                    head_response = requests.head(img_url, allow_redirects=True)
                    content_type = head_response.headers.get('Content-Type', '')
                    if 'webp' not in content_type.lower():
                        continue
                
                # 下载图片
                response = requests.get(img_url)
                if response.status_code == 200:
                    img_content = response.content
                    
                    # 检查实际内容是否为webp
                    try:
                        img_file = Image.open(BytesIO(img_content))
                        if img_file.format and img_file.format.lower() != 'webp':
                            continue
                    except Exception:
                        continue
                    
                    # 保存图片
                    filename = f"{base_filename}_img_{i}.webp"
                    filepath = os.path.join(self.images_dir, filename)
                    with open(filepath, 'wb') as f:
                        f.write(img_content)
                    
                    # 保存图片元数据
                    meta_filepath = os.path.join(self.images_dir, f"{base_filename}_img_{i}_meta.txt")
                    with open(meta_filepath, 'w', encoding='utf-8') as f:
                        f.write(f"Source URL: {img_url}\n")
                        f.write(f"Alt Text: {img_data.get('alt', '')}\n")
                        f.write(f"Width: {img_data.get('width', 'Unknown')}\n")
                        f.write(f"Height: {img_data.get('height', 'Unknown')}\n")
                    
                    saved_count += 1
                    print(f"已保存webp图片: {filepath}")
            
            except Exception as e:
                print(f"处理图片时出错: {str(e)}")
        
        print(f"已保存 {saved_count} 张webp图片")
    
    def _get_filename_from_url(self, url):
        """从URL生成文件名"""
        # 移除协议和www前缀
        filename = url.replace('http://', '').replace('https://', '').replace('www.', '')
        # 替换无效字符
        filename = filename.replace('/', '_').replace(':', '_').replace('?', '_').replace('&', '_')
        return filename
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        
        if hasattr(self, 'playwright'):
            await self.playwright.stop()


async def main_async():
    parser = argparse.ArgumentParser(description='Playwright网页内容爬取工具')
    parser.add_argument('url', help='要爬取的网页URL')
    parser.add_argument('--output', default='scraped_data', help='输出目录')
    parser.add_argument('--wait', type=int, default=5000, help='页面加载等待时间（毫秒）')
    
    args = parser.parse_args()
    
    scraper = PlaywrightScraper(output_dir=args.output)
    try:
        await scraper.scrape_url(args.url, wait_time=args.wait)
    finally:
        await scraper.close()


def main():
    """命令行入口点"""
    asyncio.run(main_async())


if __name__ == "__main__":
    main() 