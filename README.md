# 网页内容爬取工具

这是一个使用 Python 和无头浏览器的网页内容爬取工具，可以提取指定 URL 的文本内容和图片。提供了两个版本的实现：基于 Selenium 和基于 Playwright。

## 功能特点

- 自动采集网页文本内容
- 自动下载网页中的图片
- 保存完整网页截图
- 使用无头浏览器模式，不会弹出浏览器窗口
- 自动管理浏览器驱动程序

## 两种实现方式

### 1. Selenium 版本

较为传统的实现，使用 Selenium WebDriver 控制浏览器。

#### 安装依赖

```bash
pip install -r requirements.txt
```

#### 使用方法

```bash
python web_scraper.py https://example.com
```

### 2. Playwright 版本（推荐）

更现代化的实现，使用 Microsoft 的 Playwright 库控制浏览器，性能更好，功能更丰富。

#### 安装依赖

```bash
pip install -r requirements_playwright.txt
playwright install chromium
```

#### 使用方法

```bash
python web_scraper_playwright.py https://example.com
```

## 高级用法

两个版本都支持以下参数：

```bash
# Selenium版本
python web_scraper.py https://example.com --output my_data --wait 10

# Playwright版本
python web_scraper_playwright.py https://example.com --output my_data --wait 5000
```

参数说明：

- `url`：必需参数，要爬取的网页 URL
- `--output`：可选参数，指定输出目录，默认为"scraped_data"
- `--wait`：可选参数，页面加载等待时间
  - Selenium 版本：单位为秒，默认为 5 秒
  - Playwright 版本：单位为毫秒，默认为 5000 毫秒

## Playwright 版本的额外功能

Playwright 版本相比 Selenium 版本有以下改进：

1. 异步处理，性能更好
2. 更准确的页面截图（自动调整视口大小）
3. 更好的元素选择器
4. 保存图片的额外元数据（Alt 文本、尺寸等）
5. 更好地处理动态加载内容

## 输出内容

爬取的内容将保存在指定的输出目录中：

- `images/`：保存网页中的图片和整页截图
- `text/`：保存网页中的文本内容

## 环境要求

- Python 3.6+
- Chrome 浏览器

## 注意事项

- 请遵守网站的 robots.txt 规则和使用条款
- 不要过于频繁地请求同一网站，以免被封 IP
- 某些网站可能有反爬虫机制，可能需要调整等待时间或添加其他处理
