@echo off
setlocal enabledelayedexpansion

:: Default values
set HOST=127.0.0.1
set PORT=8000

:: Check if parameters are provided
if not "%~1"=="" (
    set HOST=%~1
)
if not "%~2"=="" (
    set PORT=%~2
)

echo Starting MCP Scraper Service with SSE transport...
echo Host: %HOST%
echo Port: %PORT%
echo URL: http://%HOST%:%PORT%/sse
echo.
echo Press Ctrl+C to stop the server
echo.

:: Run the MCP server using fastmcp CLI
fastmcp run mcp_scraper_service.py --transport sse --host %HOST% --port %PORT%

pause 