#!/usr/bin/env python3
"""
This script creates a .env file with the Doubao API key.
"""

import os
import sys

def create_env_file():
    """Create a .env file with the Doubao API key."""
    # Check if .env file already exists
    if os.path.exists(".env"):
        overwrite = input(".env file already exists. Overwrite? (y/n): ")
        if overwrite.lower() != "y":
            print("Aborted.")
            return
    
    # Get the API key from the user
    api_key = input("Enter your Doubao API key (default: 102e95a2-641c-41a9-aa48-ae86bdc938fa): ")
    if not api_key:
        api_key = "102e95a2-641c-41a9-aa48-ae86bdc938fa"
    
    # Get the model ID from the user
    model_id = input("Enter your Doubao model ID (default: doubao-1-5-vision-pro-32k-250115): ")
    if not model_id:
        model_id = "doubao-1-5-vision-pro-32k-250115"
    
    # Create the .env file
    with open(".env", "w") as f:
        f.write(f"DOUBAO_API_KEY={api_key}\n")
        f.write(f"DOUBAO_MODEL_ID={model_id}\n")
    
    print(".env file created successfully.")
    print(f"API key: {api_key}")
    print(f"Model ID: {model_id}")
    
    # Set environment variables for the current session
    os.environ["DOUBAO_API_KEY"] = api_key
    os.environ["DOUBAO_MODEL_ID"] = model_id
    
    print("Environment variables set for the current session.")

if __name__ == "__main__":
    create_env_file() 