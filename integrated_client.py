import asyncio
import os
import json
from fastmcp import Client

"""
This script demonstrates how to use the integrated scrape and analyze functionality
of the MCP service. It will:
1. Scrape a URL
2. Analyze each scraped image individually using Doubao Vision API
All in a single operation.

Before running this script:
1. Make sure the MCP server is running with SSE transport
2. Set the DOUBAO_API_KEY environment variable
"""

async def main():
    # Connect to the MCP server using SSE transport
    async with Client("http://127.0.0.1:8000/sse") as client:
        print("Connected to MCP server via SSE")
        
        # URL to scrape
        url = input("Enter a URL to scrape (default: https://www.python.org/): ").strip() or "https://www.python.org/"
        
        # Prompt for image analysis
        default_prompt = ("识别图片中的文字，提取其中关于发票开具的要素信息，支持个人和企业发票\n" 
                        "# 输出约定\n" 
                        " 1.抬头为公司时，输出{抬头},{税号},{邮箱},{金额}\n" 
                        " 2.抬头为个人时，输出{抬头},{个人},{邮箱},{金额}\n" 
                        " 3.如图片中信息不全，输出{开票信息无法识别,请提交完整信息,缺失:{缺失项}}")
        custom_prompt = input(f"Enter prompt for image analysis (press Enter for default invoice prompt): \n").strip()
        analysis_prompt = custom_prompt if custom_prompt else default_prompt
        
        print(f"\nScraping URL and analyzing images individually: {url}")
        
        # Call the integrated scrape_and_analyze tool
        tool_params = {"url": url, "prompt": analysis_prompt}
        result = await client.call_tool("scrape_and_analyze", tool_params)
        
        # Process the result
        if result:
            for content in result:
                if hasattr(content, 'text'):
                    try:
                        data = json.loads(content.text)
                        if data['success']:
                            print(f"\n✅ Successfully processed {url}")
                            
                            # Print text content summary
                            if 'text_content' in data and data['text_content']:
                                print("\n📄 Text Content Summary:")
                                print(data['text_content'][:200] + "..." if len(data['text_content']) > 200 else data['text_content'])
                            
                            # Print image information
                            print(f"\n🖼️ Found {data['image_count']} images.")
                            
                            if 'image_analyses' in data and data['image_analyses']:
                                print("\n🔍 Image Analysis Results:")
                                for i, analysis_item in enumerate(data['image_analyses']):
                                    print(f"\n--- Analysis for Image {i+1} ({analysis_item['image_path']}) ---")
                                    print(analysis_item['analysis'])
                            elif data['image_count'] > 0:
                                print("No analysis results returned for images.")
                            else:
                                print("No images were analyzed.")
                                
                        else:
                            print(f"\n❌ Failed to process {url}: {data.get('error', 'Unknown error')}")
                    except json.JSONDecodeError:
                        print(f"Error parsing result: {content.text}")
        else:
            print("No result received from scrape_and_analyze tool.")

if __name__ == "__main__":
    # Check if DOUBAO_API_KEY is set
    if not os.environ.get("DOUBAO_API_KEY"):
        print("Warning: DOUBAO_API_KEY environment variable is not set.")
        print("Image analysis will not work without this key.")
        print("You can set it with: export DOUBAO_API_KEY=your_api_key")
        print("Or run the setup_env.py script to create a .env file.")
        print()
    
    asyncio.run(main()) 