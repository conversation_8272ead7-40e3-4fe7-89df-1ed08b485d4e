import asyncio
import json
import os
from fastmcp import Client, FastMC<PERSON>
from web_scraper_playwright import PlaywrightScraper

"""
This example demonstrates how to use Context7 with the MCP service.
Context7 is a feature that allows LLMs to access libraries and documentation.

In this example, we'll create a script that:
1. Connects to our MCP service
2. Scrapes a URL
3. Uses Context7 to analyze the content with a specific library
"""

async def main():
    # Connect to the MCP server
    async with Client("mcp_scraper_service.py") as client:
        print("Connected to MCP server")
        
        # Example URL to scrape (a Python library documentation)
        url = "https://fastapi.tiangolo.com/"
        
        print(f"\nScraping URL: {url}")
        # Call the scrape_url tool
        result = await client.call_tool("scrape_url", {"url": url})
        
        # Process the result
        if result:
            for content in result:
                if hasattr(content, 'text'):
                    try:
                        data = json.loads(content.text)
                        if data['success']:
                            print(f"Successfully scraped {url}")
                            
                            # Read the scraped content
                            content, _ = await client.read_resource(f"scraped-content://{url}")
                            
                            # Create a temporary file with the content
                            temp_file = "temp_scraped_content.txt"
                            with open(temp_file, "w", encoding="utf-8") as f:
                                f.write(content)
                            
                            print("\nAnalyzing the scraped content with Context7...")
                            print("This would typically be done by an LLM with Context7 capabilities.")
                            print("For demonstration, we'll simulate what an LLM might do:")
                            
                            # Simulate Context7 analysis
                            print("\n--- Simulated Context7 Analysis ---")
                            print("1. Identified FastAPI documentation")
                            print("2. Found key concepts: Path operations, Query parameters, Request bodies")
                            print("3. Recognized Python code examples")
                            print("4. Detected OpenAPI integration references")
                            print("--- End of Simulated Analysis ---\n")
                            
                            # Clean up
                            os.remove(temp_file)
                            print(f"Removed temporary file: {temp_file}")
                        else:
                            print(f"Failed to scrape {url}: {data.get('error', 'Unknown error')}")
                    except json.JSONDecodeError:
                        print(f"Error parsing result: {content.text}")

if __name__ == "__main__":
    asyncio.run(main()) 