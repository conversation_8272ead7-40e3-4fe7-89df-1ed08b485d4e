@echo off
echo ========================================
echo MCP Web Scraper Service
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.12 or later
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found
    echo Please copy .env.example to .env and configure your API keys
    echo.
)

REM Start the server
echo Starting MCP Web Scraper Service...
echo Press Ctrl+C to stop the server
echo.
python start_server.py

pause
