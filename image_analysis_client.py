import asyncio
import os
import json
from fastmcp import Client

"""
This script demonstrates how to use the image merging and analysis functionality
of the MCP service. It will:
1. Scrape a URL to get images
2. Merge the images vertically
3. Analyze the merged image using Doubao Vision API

Before running this script:
1. Make sure the MCP server is running with SSE transport
2. Set the DOUBAO_API_KEY environment variable
"""

async def main():
    # Connect to the MCP server using SSE transport
    async with Client("http://127.0.0.1:8000/sse") as client:
        print("Connected to MCP server via SSE")
        
        # URL to scrape
        url = input("Enter a URL to scrape (default: https://python.org): ").strip() or "https://python.org"
        
        print(f"\nScraping URL: {url}")
        # Call the scrape_url tool to get images
        scrape_result = await client.call_tool("scrape_url", {"url": url})
        
        # Process the scrape result
        if scrape_result:
            for content in scrape_result:
                if hasattr(content, 'text'):
                    try:
                        data = json.loads(content.text)
                        if data['success']:
                            print(f"Successfully scraped {url}")
                            print(f"Found {data['image_count']} images")
                            
                            if data['image_count'] == 0:
                                print("No images found to merge and analyze.")
                                return
                            
                            # Merge the images vertically
                            print("\nMerging images vertically...")
                            merge_result = await client.call_tool("merge_images_vertically", {"url": url})
                            
                            merged_image_path = None
                            for content in merge_result:
                                if hasattr(content, 'text'):
                                    try:
                                        merge_data = json.loads(content.text)
                                        if merge_data['success']:
                                            merged_image_path = merge_data['merged_image_path']
                                            print(f"Successfully merged {merge_data['original_image_count']} images")
                                            print(f"Merged image saved to: {merged_image_path}")
                                        else:
                                            print(f"Failed to merge images: {merge_data.get('error', 'Unknown error')}")
                                    except json.JSONDecodeError:
                                        print(f"Error parsing merge result: {content.text}")
                            
                            if not merged_image_path:
                                print("Failed to get merged image path.")
                                return
                            
                            # Check if DOUBAO_API_KEY is set
                            if not os.environ.get("DOUBAO_API_KEY"):
                                print("\nDOUBAO_API_KEY environment variable is not set.")
                                print("Skipping image analysis.")
                                return
                            
                            # Analyze the merged image
                            print("\nAnalyzing the merged image...")
                            
                            # Get the invoice analysis prompt
                            prompt_result = await client.get_prompt("invoice_analysis_prompt")
                            invoice_prompt = ""
                            for message in prompt_result.messages:
                                invoice_prompt = message.content.text.strip()
                            
                            # Call the analyze_image tool
                            analysis_result = await client.call_tool("analyze_image", {
                                "image_path": merged_image_path,
                                "prompt": invoice_prompt
                            })
                            
                            # Print the analysis result
                            print("\nImage Analysis Result:")
                            for content in analysis_result:
                                if hasattr(content, 'text'):
                                    print(content.text)
                        else:
                            print(f"Failed to scrape {url}: {data.get('error', 'Unknown error')}")
                    except json.JSONDecodeError:
                        print(f"Error parsing scrape result: {content.text}")
        else:
            print("No result received from scrape_url tool.")

if __name__ == "__main__":
    # Check if DOUBAO_API_KEY is set
    if not os.environ.get("DOUBAO_API_KEY"):
        print("Warning: DOUBAO_API_KEY environment variable is not set.")
        print("Image analysis will not work without this key.")
        print("You can set it with: export DOUBAO_API_KEY=your_api_key")
        print()
    
    asyncio.run(main()) 