#!/usr/bin/env python3
"""
Simple startup script for the MCP Web Scraper Service
"""

import asyncio
import sys
from mcp_scraper_service import start_server, config, logger

def main():
    """Main entry point for the server"""
    print("=" * 60)
    print("MCP Web Scraper Service")
    print("=" * 60)
    print(f"Output directory: {config.output_dir}")
    print(f"Default host: {config.default_host}")
    print(f"Default port: {config.default_port}")
    print(f"Doubao API available: {'Yes' if config.doubao_api_key else 'No'}")
    print("=" * 60)
    
    if not config.doubao_api_key:
        print("⚠️  Warning: DOUBAO_API_KEY not set. Image analysis will be disabled.")
        print("   Set the environment variable to enable image analysis.")
        print()
    
    print("Starting server...")
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        logger.error(f"Server startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
