# Web Scraper MCP Service

This project provides a Model Context Protocol (MCP) service for web scraping using Playwright. It allows LLMs and other applications to scrape web content through a standardized interface.

## Features

- Scrape web pages with Playwright
- Extract text content and images
- Save screenshots of web pages
- Merge scraped images vertically into a single image (optional utility)
- **Analyze each scraped image individually using Doubao Vision API**
- **Integrated scrape and individual image analysis in a single operation**
- Progress reporting during scraping
- Access previously scraped content
- Prompts for content analysis
- Support for SSE (Server-Sent Events) transport

## Installation

### Prerequisites

- Python 3.12+
- Playwright
- FastMCP 2.6.1
- OpenAI Python SDK (for Doubao Vision API)
- python-dotenv

### Setup

1. Install the required packages:

```bash
pip install fastmcp==2.6.1 playwright pillow requests beautifulsoup4 openai python-dotenv
```

2. Install Playwright browsers:

```bash
playwright install chromium
```

3. Set up the Doubao API key (for image analysis):

```bash
python setup_env.py
```

Or manually create a `.env` file with:

```
DOUBAO_API_KEY=your_api_key
DOUBAO_MODEL_ID=doubao-1-5-vision-pro-32k-250115 # Or your preferred model
```

## Usage

### Running the MCP Server

#### Using SSE Transport (Recommended for Web Applications)

To start the MCP server with SSE transport:

```bash
python mcp_scraper_service.py
```

This will start the server on `http://127.0.0.1:8000/sse` by default.

You can customize the host and port:

```bash
fastmcp run mcp_scraper_service.py --transport sse --host 0.0.0.0 --port 9000
```

#### Using STDIO Transport (For Command-Line Applications)

To use STDIO transport instead of SSE, modify the last line in `mcp_scraper_service.py`:

```python
if __name__ == "__main__":
    mcp.run(transport="stdio")  # Default transport
```

### Available MCP Components

#### Tools

1.  `scrape_url` - Basic web scraping tool.

    - Parameters: `url` (str), `wait_time` (int, optional), `output_dir` (str, optional).
    - Returns: Dictionary with scrape results including paths to text and images.

2.  `scrape_url_with_progress` - Web scraping with progress reporting.

    - Parameters: `url` (str), `wait_time` (int, optional), `output_dir` (str, optional).
    - Returns: Similar to `scrape_url` but with progress updates.

3.  `merge_images_vertically` - Merge scraped images from a URL vertically into a single image.

    - Parameters: `url` (str), `output_dir` (str, optional).
    - Returns: Dictionary with the path to the merged image.

4.  `analyze_image` - Analyze a single image file using the Doubao Vision API.

    - Parameters: `image_path` (str), `prompt` (str, optional).
    - Returns: Analysis result (string) from the API.

5.  `scrape_and_analyze` - **Integrated tool: scrapes a URL and analyzes each image individually.**
    - Parameters:
      - `url` (string): The URL to scrape.
      - `wait_time` (integer, optional): Wait time in milliseconds (default: 5000).
      - `output_dir` (string, optional): Output directory (default: "scraped_data").
      - `prompt` (string, optional): The prompt for image analysis (default: invoice analysis prompt).
    - Returns: Dictionary with text content, image paths, and a list of `image_analyses`. Each item in `image_analyses` contains `image_path` and `analysis` (string).

#### Resources

1.  `scraped-content://{url}` - Access previously scraped text content.
    - Parameters: `url` (str).

#### Prompts

1.  `scrape_request` - Generate a prompt for analyzing scraped content.

    - Parameters: `url` (str).

2.  `invoice_analysis_prompt` - Create a prompt for analyzing invoice images.
    - No parameters.

### Example Clients

1.  `mcp_client_example.py` - Basic client for scraping and reading content.
2.  `image_analysis_client.py` - Client for standalone image merging and analysis tools.
3.  `integrated_client.py` - **Client for the integrated `scrape_and_analyze` functionality (individual image analysis).**

To run the integrated client:

```bash
python integrated_client.py
```

Or use the provided scripts:

```bash
# Windows
run_integrated_client.bat

# Unix/Linux/macOS
./run_integrated_client.sh # Make sure it's executable: chmod +x run_integrated_client.sh
```

This script demonstrates:

- Connecting to the MCP server via SSE.
- Scraping a URL and analyzing each image individually in a single operation.
- Displaying the text content summary and individual image analysis results.

## Image Processing Features

### Vertical Image Merging (Optional)

The `merge_images_vertically` tool combines all scraped images from a URL into a single vertical image. This can be used as a standalone utility.

### Individual Image Analysis with Doubao Vision

The `analyze_image` tool (and the analysis step in `scrape_and_analyze`) uses the Doubao Vision API to analyze images. It can:

- Recognize text in images.
- Extract information from invoices and receipts.
- Identify objects and scenes.
- Provide detailed descriptions of image content.

For invoice analysis, the default prompt in `scrape_and_analyze` or the `invoice_analysis_prompt` can be used to extract specific information.

### Integrated Scrape and Individual Image Analysis

The `scrape_and_analyze` tool streamlines the process:

1.  Scrapes the URL to extract text and paths to all images.
2.  Iterates through each scraped image.
3.  Analyzes each image individually using the Doubao Vision API with the provided (or default) prompt.
4.  Returns the text content along with a list of analysis results, one for each image.

This is ideal for:

- Automating the entire process of scraping and detailed image-by-image analysis with a single API call.
- Simplifying client code.

## Integration with LLMs

### Using with Claude

```python
import anthropic
from fastmcp import Client

# Setup MCP client with SSE transport
mcp_client = Client("http://127.0.0.1:8000/sse")

# Setup Anthropic client
client = anthropic.Anthropic()

async def main():
    async with mcp_client:
        response = client.beta.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=1000,
            messages=[
                {"role": "user", "content": "Scrape and analyze the content from https://example.com"}
            ],
            mcp_servers=[mcp_client.session],
            extra_headers={
                "anthropic-beta": "mcp-client-2025-04-04"
            }
        )
        print(response.content)
```

### Using with OpenAI

```python
from openai import OpenAI
from fastmcp import Client

# Setup MCP client with SSE transport
mcp_client = Client("http://127.0.0.1:8000/sse")

# Setup OpenAI client
client = OpenAI()

async def main():
    async with mcp_client:
        response = client.responses.create(
            model="gpt-4",
            tools=[{"type": "mcp", "server": mcp_client.session}],
            input="Scrape and analyze the content from https://example.com"
        )
        print(response.output_text)
```

## Advanced Configuration

### Running with Different Transports

FastMCP supports three transport protocols:

1. **STDIO**: Best for local tools and command-line scripts

   ```bash
   fastmcp run mcp_scraper_service.py --transport stdio
   ```

2. **SSE**: For web applications and compatibility with existing SSE clients

   ```bash
   fastmcp run mcp_scraper_service.py --transport sse --host 127.0.0.1 --port 8000
   ```

3. **Streamable HTTP**: Recommended for production web deployments
   ```bash
   fastmcp run mcp_scraper_service.py --transport streamable-http --host 127.0.0.1 --port 8000 --path /mcp
   ```

### Customizing Output Directory

You can customize the output directory when calling the tools:

```python
result = await client.call_tool("scrape_url", {
    "url": "https://example.com",
    "output_dir": "custom_output"
})
```

### Customizing Image Analysis Prompts

You can provide custom prompts for image analysis:

```python
result = await client.call_tool("analyze_image", {
    "image_path": "path/to/image.jpg",
    "prompt": "识别图片中的文字,提取其中关于发票开具的要素信息"
})
```

### Using the Integrated Tool with Custom Parameters

```python
result = await client.call_tool("scrape_and_analyze", {
    "url": "https://example.com",
    "wait_time": 10000,  # Wait 10 seconds for dynamic content
    "output_dir": "collected_data",
    "prompt": "Describe this image in detail."
})
# Process result['image_analyses'] which is a list of analysis for each image
```

## Troubleshooting

- If you encounter browser-related issues, make sure Playwright is properly installed with `playwright install`
- For connection issues, verify that the MCP server is running on the correct host and port
- Check permissions for writing to the output directory
- If using SSE, ensure your firewall allows connections on the specified port
- For image analysis issues, verify that the `DOUBAO_API_KEY` environment variable is set correctly and the model ID is appropriate

## License

This project is licensed under the MIT License - see the LICENSE file for details.
