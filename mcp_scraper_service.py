import os
import asyncio
import logging
import datetime
import socket
import sys
import subprocess
import signal

import requests
from PIL import Image
from fastmcp import FastMCP, Context
from dotenv import load_dotenv

from web_scraper_playwright import PlaywrightScraper
from image_analysis import process_image_file

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configuration
class Config:
    """Configuration management for MCP scraper service"""

    def __init__(self):
        self.doubao_api_key = os.getenv('DOUBAO_API_KEY')
        self.doubao_model_id = os.getenv('DOUBAO_MODEL_ID', 'doubao-1-5-vision-pro-32k-250115')
        self.default_port = int(os.getenv('MCP_PORT', '8000'))
        self.default_host = os.getenv('MCP_HOST', '127.0.0.1')
        self.output_dir = os.getenv('OUTPUT_DIR', 'scraped_data')
        self.max_wait_time = int(os.getenv('MAX_WAIT_TIME', '30000'))

    def validate(self) -> bool:
        """Validate required configuration"""
        if not self.doubao_api_key:
            logger.warning("DOUBAO_API_KEY environment variable not set. Image analysis will not work.")
            return False
        return True

config = Config()

# Create an MCP server
mcp = FastMCP("Web Scraper MCP")

@mcp.prompt()
def comprehensive_image_analysis_prompt() -> str:
    """
    Create a comprehensive prompt for detailed image analysis

    Returns:
        A structured prompt for thorough image analysis
    """
    return """
    请详细分析这张图片中的内容，遵循以下结构进行分析：

    1. 图片类型：判断是否为照片、截图、图表、插图等
    2. 主要内容：描述图片中的主要对象、人物或场景
    3. 文字内容：提取并整理图片中的所有可见文字
    4. 上下文信息：分析图片可能的来源、目的和适用场景
    5. 关键信息点：提取对理解内容最重要的3-5个关键点
    6. 总结：用1-2句话概括图片的整体含义和价值

    请尽可能详细地提取图片中的所有信息，特别是文字内容。
    """

class ImageDownloader:
    """Enhanced image downloader with retry and validation"""

    def __init__(self):
        self.default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
        }

    async def download_image(self, url: str, output_path: str, headers: dict = None) -> bool:
        """
        Download image with retry and validation

        Args:
            url: Image URL to download
            output_path: Where to save the image
            headers: Optional HTTP headers

        Returns:
            bool: Success status
        """
        headers = headers or self.default_headers

        for attempt in range(3):
            try:
                logger.info(f"Downloading image (attempt {attempt + 1}): {url}")
                response = requests.get(url, headers=headers, timeout=10, stream=True)
                response.raise_for_status()

                # Check if it's actually an image
                content_type = response.headers.get('Content-Type', '')
                if not content_type.startswith('image/'):
                    logger.warning(f"URL {url} returned non-image content type: {content_type}")
                    return False

                # Save the image
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                # Verify the image can be opened
                try:
                    with Image.open(output_path) as img:
                        logger.info(f"Successfully downloaded and validated image: {output_path}")
                        return True
                except Exception as e:
                    logger.error(f"Downloaded file is not a valid image: {e}")
                    if os.path.exists(output_path):
                        os.remove(output_path)
                    return False

            except requests.exceptions.RequestException as e:
                logger.warning(f"Download attempt {attempt+1} failed: {e}")
                if attempt < 2:  # Don't sleep on the last attempt
                    await asyncio.sleep(1)

        logger.error(f"Failed to download image after 3 attempts: {url}")
        return False

# Global image downloader instance
image_downloader = ImageDownloader()

async def _extract_and_process_images(scraper: PlaywrightScraper, url: str, ctx: Context = None) -> list:
    """
    Extract and process images from the scraped page

    Args:
        scraper: The PlaywrightScraper instance
        url: The source URL
        ctx: Context for progress reporting

    Returns:
        List of saved image file paths
    """
    from urllib.parse import urljoin

    # Get all image URLs from the page
    img_srcs = await scraper.page.evaluate("""() => {
        const images = Array.from(document.querySelectorAll('img'));
        return images.map(img => {
            return {
                src: img.src,
                alt: img.alt || '',
                width: img.width || 0,
                height: img.height || 0,
                naturalWidth: img.naturalWidth || 0,
                naturalHeight: img.naturalHeight || 0
            };
        }).filter(img =>
            img.src &&
            !img.src.startsWith('data:') &&
            img.naturalWidth > 50 &&
            img.naturalHeight > 50
        );
    }""")

    base_filename = scraper._get_filename_from_url(url)
    saved_images = []

    for i, img_data in enumerate(img_srcs):
        try:
            img_url = img_data['src']

            # Handle relative URLs
            if not img_url.startswith('http'):
                img_url = urljoin(url, img_url)

            # Check if it's an image
            try:
                head_response = requests.head(img_url, allow_redirects=True, timeout=5)
                content_type = head_response.headers.get('Content-Type', '')
                if 'image' not in content_type.lower():
                    continue
            except Exception as e:
                logger.warning(f"Error checking image headers: {e}")
                continue

            # Prepare output path
            filename = f"{base_filename}_img_{i}.webp"
            filepath = os.path.join(scraper.images_dir, filename)

            # Download the image
            if ctx:
                await ctx.info(f"Downloading image {i+1}/{len(img_srcs)}: {img_url}")

            success = await image_downloader.download_image(img_url, filepath)

            if success:
                # Convert to webp if needed
                try:
                    with Image.open(filepath) as img:
                        if img.format != 'WEBP':
                            img.save(filepath, "WEBP", quality=85)
                except Exception as e:
                    logger.warning(f"Error converting to webp: {e}")
                    continue

                # Save metadata
                meta_filepath = os.path.join(scraper.images_dir, f"{base_filename}_img_{i}_meta.txt")
                with open(meta_filepath, 'w', encoding='utf-8') as f:
                    f.write(f"Source URL: {img_url}\n")
                    f.write(f"Alt Text: {img_data.get('alt', '')}\n")
                    f.write(f"Width: {img_data.get('width', 'Unknown')}\n")
                    f.write(f"Height: {img_data.get('height', 'Unknown')}\n")
                    f.write(f"Natural Width: {img_data.get('naturalWidth', 'Unknown')}\n")
                    f.write(f"Natural Height: {img_data.get('naturalHeight', 'Unknown')}\n")

                saved_images.append(filepath)
                if ctx:
                    await ctx.info(f"Saved image: {filepath}")
        except Exception as e:
            if ctx:
                await ctx.warning(f"Error processing image: {str(e)}")
            logger.error(f"Error processing image {img_url}: {e}")

    return saved_images

@mcp.tool()
async def scrape_url_analyze_content(
    url: str,
    wait_time: int = 15000,
    custom_prompt: str = None,
    ctx: Context = None
) -> dict:
    """
    Scrape a URL, extract text content and analyze all images

    Args:
        url: The URL to scrape
        wait_time: Wait time in milliseconds (default: 15000)
        custom_prompt: Optional custom prompt for image analysis
        ctx: Context object for progress reporting (injected automatically)

    Returns:
        A dictionary with the text content and image analysis results
    """
    if ctx:
        await ctx.info(f"Starting to scrape and analyze: {url}")
        await ctx.report_progress(10, 100)

    scraper = None
    try:
        # Validate configuration
        if not config.validate():
            logger.warning("Configuration validation failed, continuing without image analysis")

        # Initialize the scraper
        scraper = PlaywrightScraper(output_dir=config.output_dir)

        # Set up the browser
        if ctx:
            await ctx.info("Setting up browser...")
            await ctx.report_progress(15, 100)

        await scraper._setup_browser()

        # Navigate to URL
        if ctx:
            await ctx.info(f"Navigating to {url}...")
            await ctx.report_progress(20, 100)

        # Navigate with timeout
        await scraper.page.goto(url, wait_until="networkidle", timeout=wait_time)

        # Wait for dynamic content
        if wait_time > 0:
            if ctx:
                await ctx.info("Waiting for dynamic content to load...")
            await scraper.page.wait_for_timeout(min(wait_time // 2, 10000))

        # Extract text content
        if ctx:
            await ctx.info("Extracting text content...")
            await ctx.report_progress(40, 100)

        await scraper._extract_and_save_text(url)
        
        # Extract and download images
        if ctx:
            await ctx.info("Extracting and downloading images...")
            await ctx.report_progress(50, 100)

        saved_images = await _extract_and_process_images(scraper, url, ctx)

        if ctx:
            await ctx.info(f"Saved {len(saved_images)} images")
        
        # Get the text content
        base_filename = scraper._get_filename_from_url(url)
        text_path = os.path.join(scraper.text_dir, f"{base_filename}.txt")
        text_content = ""
        if os.path.exists(text_path):
            with open(text_path, 'r', encoding='utf-8') as f:
                text_content = f.read()

        # Analyze images if API key is available
        image_analyses = []
        if config.doubao_api_key and saved_images:
            if ctx:
                await ctx.info(f"Analyzing {len(saved_images)} images with Doubao Vision...")
                await ctx.report_progress(70, 100)

            # Use the custom prompt if provided, otherwise use the default
            analysis_prompt = custom_prompt if custom_prompt else comprehensive_image_analysis_prompt()

            for i, image_path in enumerate(saved_images):
                if ctx:
                    current_progress = 70 + int((i / max(len(saved_images), 1)) * 25)
                    await ctx.report_progress(current_progress, 100)

                try:
                    # Get image metadata for enhanced analysis
                    meta_path = image_path.replace(".webp", "_meta.txt")
                    meta_info = ""
                    if os.path.exists(meta_path):
                        with open(meta_path, 'r', encoding='utf-8') as f:
                            meta_info = f.read()

                    # Enhanced analysis with metadata context
                    enhanced_prompt = f"""
                    图片元数据信息：
                    {meta_info}

                    {analysis_prompt}
                    """

                    analysis = await asyncio.to_thread(process_image_file, image_path, enhanced_prompt)
                    image_analyses.append({
                        "image_path": image_path,
                        "metadata": meta_info,
                        "analysis": analysis
                    })
                except Exception as e:
                    if ctx:
                        await ctx.warning(f"Failed to analyze image {image_path}: {str(e)}")
                    logger.error(f"Failed to analyze image {image_path}: {e}")
                    image_analyses.append({
                        "image_path": image_path,
                        "analysis": f"Error analyzing image: {str(e)}"
                    })
        else:
            if ctx:
                await ctx.info("Skipping image analysis (no API key or no images found)")
                await ctx.report_progress(95, 100)
        
        # Create comprehensive analysis
        if ctx:
            await ctx.info("Creating comprehensive page analysis...")
            await ctx.report_progress(95, 100)

        # Prepare the final result
        final_result = {
            "success": True,
            "url": url,
            "page_title": await scraper.page.title(),
            "text_content": text_content,
            "image_count": len(saved_images),
            "image_analyses": image_analyses,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "config_status": {
                "doubao_api_available": bool(config.doubao_api_key),
                "output_directory": config.output_dir
            }
        }

        if ctx:
            await ctx.info("Scraping and analysis completed!")
            await ctx.report_progress(100, 100)

        logger.info(f"Successfully processed {url}: {len(saved_images)} images, {len(text_content)} chars text")
        return final_result

    except Exception as e:
        error_msg = f"Error during scrape and analyze: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)
            await ctx.report_progress(100, 100)
        return {
            "success": False,
            "url": url,
            "error": str(e),
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    finally:
        if scraper:
            await scraper.close()

class ServerManager:
    """Manage MCP server startup and port handling"""

    @staticmethod
    def is_port_in_use(port: int) -> bool:
        """Check if a port is already in use."""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('127.0.0.1', port))
                return False
            except socket.error:
                return True

    @staticmethod
    def find_process_using_port(port: int) -> int:
        """Find the process ID using the specified port."""
        try:
            if sys.platform.startswith('win'):
                # Windows
                output = subprocess.check_output(f'netstat -ano | findstr :{port}', shell=True).decode()
                if output:
                    lines = output.strip().split('\n')
                    for line in lines:
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.strip().split()
                            return int(parts[-1])
            else:
                # Linux/Mac
                output = subprocess.check_output(f'lsof -i :{port} -t', shell=True).decode()
                if output:
                    return int(output.strip())
        except (subprocess.SubprocessError, ValueError):
            pass
        return None

    @staticmethod
    def kill_process(pid: int) -> bool:
        """Kill a process by its PID."""
        try:
            if sys.platform.startswith('win'):
                subprocess.check_call(f'taskkill /F /PID {pid}', shell=True)
            else:
                subprocess.check_call(f'kill -9 {pid}', shell=True)
            return True
        except subprocess.SubprocessError:
            return False

    @staticmethod
    def find_available_port(start_port: int = 8000, max_attempts: int = 10) -> int:
        """Find an available port starting from start_port"""
        for port in range(start_port, start_port + max_attempts):
            if not ServerManager.is_port_in_use(port):
                return port
        raise RuntimeError(f"Could not find an available port between {start_port} and {start_port + max_attempts - 1}")

server_manager = ServerManager()

async def start_server():
    """Start the MCP server with proper initialization"""
    logger.info("Initializing MCP server...")

    # Validate configuration
    config.validate()

    # Pre-initialize resources
    logger.info("Pre-initializing resources...")
    os.makedirs(os.path.join(config.output_dir, "images"), exist_ok=True)
    os.makedirs(os.path.join(config.output_dir, "text"), exist_ok=True)

    # Test Playwright installation
    try:
        logger.info("Testing Playwright initialization...")
        PlaywrightScraper()  # Just test instantiation
    except Exception as e:
        logger.warning(f"PlaywrightScraper test initialization failed: {e}")
        logger.warning("This may not affect server operation, continuing...")

    # Find available port
    try:
        port = server_manager.find_available_port(config.default_port)
        logger.info(f"Using port {port}")
    except RuntimeError as e:
        logger.error(f"Port allocation failed: {e}")
        raise

    # Start the server
    logger.info(f"Starting MCP server on {config.default_host}:{port}...")
    try:
        await mcp.run_async(
            transport="sse",
            host=config.default_host,
            port=port,
            lifespan="on",
            log_level="info",
            timeout_keep_alive=65
        )
    except Exception as e:
        logger.error(f"Error during MCP server startup: {e}")
        raise

if __name__ == "__main__":
    # Check Playwright installation
    try:
        from playwright.async_api import async_playwright  # noqa: F401
        logger.info("Playwright is properly installed.")
    except ImportError:
        logger.error("Playwright is not installed. Please install it with: pip install playwright")
        logger.error("After installation, run: playwright install")
        exit(1)

    logger.info("Starting Web Scraper MCP with Doubao image analysis...")
    logger.info("This service will extract text and analyze images from websites.")

    # Set up signal handlers for graceful shutdown
    def signal_handler(sig, frame):  # noqa: ARG001
        logger.info("Received shutdown signal, exiting gracefully...")
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Start the server
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()