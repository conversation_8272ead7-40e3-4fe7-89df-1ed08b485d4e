import os
import asyncio
from fastmcp import FastMCP, Context
from web_scraper_playwright import PlaywrightScraper
from PIL import Image
import io
import base64
from image_analysis import process_image, process_image_file
from dotenv import load_dotenv
import datetime
import requests
from urllib.parse import urlparse, urljoin
import socket
import sys
import time
import subprocess
import signal

# Load environment variables
load_dotenv()

# Check for required API keys
if not os.getenv('DOUBAO_API_KEY'):
    print("Warning: DOUBAO_API_KEY environment variable not set. Image analysis will not work.")

# Create an MCP server
mcp = FastMCP("Web Scraper MCP")

@mcp.prompt()
def simple_image_analysis_prompt() -> str:
    """
    Create a simplified prompt for general image analysis using Context7 concepts
    
    Returns:
        A prompt for general image analysis
    """
    return """
    请详细分析这张图片中的内容，遵循以下结构进行分析：
    
    1. 图片类型：判断是否为照片、截图、图表、插图等
    2. 主要内容：描述图片中的主要对象、人物或场景
    3. 文字内容：提取并整理图片中的所有可见文字
    4. 上下文信息：分析图片可能的来源、目的和适用场景
    5. 关键信息点：提取对理解内容最重要的3-5个关键点
    6. 总结：用1-2句话概括图片的整体含义和价值
    
    请尽可能详细地提取图片中的所有信息，特别是文字内容。
    """

async def download_image(url, output_path, headers=None):
    """
    Enhanced image downloader with retry and validation
    
    Args:
        url: Image URL to download
        output_path: Where to save the image
        headers: Optional HTTP headers
    
    Returns:
        bool: Success status
    """
    if not headers:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
        }
    
    # Try up to 3 times
    for attempt in range(3):
        try:
            response = requests.get(url, headers=headers, timeout=10, stream=True)
            response.raise_for_status()
            
            # Check if it's actually an image
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                print(f"Warning: URL {url} returned non-image content type: {content_type}")
                return False
            
            # Save the image
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Verify the image can be opened
            try:
                with Image.open(output_path) as img:
                    # Image is valid
                    return True
            except Exception as e:
                print(f"Downloaded file is not a valid image: {e}")
                if os.path.exists(output_path):
                    os.remove(output_path)
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"Download attempt {attempt+1} failed: {e}")
            await asyncio.sleep(1)  # Wait before retry
    
    return False

@mcp.tool()
async def scrape_url_analyze_content(url: str, wait_time: int = 15000, custom_prompt: str = None, ctx: Context = None) -> dict:
    """
    Scrape a URL, extract text content and analyze all webp images in one call
    
    Args:
        url: The URL to scrape
        wait_time: Wait time in milliseconds (default: 15000)
        custom_prompt: Optional custom prompt for image analysis (default: None, uses simple_image_analysis_prompt)
        ctx: Context object for progress reporting (injected automatically)
    
    Returns:
        A dictionary with the text content and image analysis results
    """
    if ctx:
        await ctx.info(f"Starting to scrape and analyze: {url}")
        await ctx.report_progress(10, 100)
    
    scraper = None
    try:
        # Initialize the scraper
        output_dir = "scraped_data"
        scraper = PlaywrightScraper(output_dir=output_dir)
        
        # Explicitly set up the browser before scraping
        if ctx:
            await ctx.info("Setting up browser...")
            await ctx.report_progress(15, 100)
        
        await scraper._setup_browser()
        
        # Navigate to URL
        if ctx:
            await ctx.info(f"Navigating to {url}...")
            await ctx.report_progress(20, 100)
        
        # Enable Context7-inspired navigation with better waiting strategy
        await scraper.page.goto(url, wait_until="networkidle", timeout=wait_time)
        
        # Wait for dynamic content
        if wait_time > 0:
            if ctx:
                await ctx.info("Waiting for dynamic content to load...")
            await scraper.page.wait_for_timeout(wait_time / 2)  # Additional wait time for JS rendering
        
        # Extract text content with improved parsing
        if ctx:
            await ctx.info("Extracting text content...")
            await ctx.report_progress(40, 100)
        
        await scraper._extract_and_save_text(url)
        
        # Enhanced image extraction
        if ctx:
            await ctx.info("Extracting and downloading images...")
            await ctx.report_progress(50, 100)
        
        # Get all image URLs from the page with enhanced selector
        img_srcs = await scraper.page.evaluate("""() => {
            const images = Array.from(document.querySelectorAll('img'));
            return images.map(img => {
                return {
                    src: img.src,
                    alt: img.alt || '',
                    width: img.width || 0,
                    height: img.height || 0,
                    naturalWidth: img.naturalWidth || 0,
                    naturalHeight: img.naturalHeight || 0
                };
            }).filter(img => 
                img.src && 
                !img.src.startsWith('data:') && 
                img.naturalWidth > 50 && 
                img.naturalHeight > 50
            );
        }""")
        
        # Process found images
        base_filename = scraper._get_filename_from_url(url)
        saved_images = []
        
        for i, img_data in enumerate(img_srcs):
            try:
                img_url = img_data['src']
                
                # Handle relative URLs
                if not img_url.startswith('http'):
                    img_url = urljoin(url, img_url)
                
                # Check if it's a webp image or could be converted to one
                is_webp = img_url.lower().endswith('.webp')
                
                if not is_webp:
                    # Check content type
                    try:
                        head_response = requests.head(img_url, allow_redirects=True, timeout=5)
                        content_type = head_response.headers.get('Content-Type', '')
                        if 'webp' not in content_type.lower() and 'image' not in content_type.lower():
                            continue
                    except Exception as e:
                        print(f"Error checking image headers: {e}")
                        continue
                
                # Prepare output path
                filename = f"{base_filename}_img_{i}.webp"
                filepath = os.path.join(scraper.images_dir, filename)
                
                # Download the image
                if ctx:
                    await ctx.info(f"Downloading image {i+1}/{len(img_srcs)}: {img_url}")
                
                success = await download_image(img_url, filepath)
                
                if success:
                    # Convert to webp if needed
                    if not is_webp:
                        try:
                            with Image.open(filepath) as img:
                                webp_path = filepath
                                img.save(webp_path, "WEBP", quality=85)
                        except Exception as e:
                            print(f"Error converting to webp: {e}")
                            continue
                    
                    # Save metadata
                    meta_filepath = os.path.join(scraper.images_dir, f"{base_filename}_img_{i}_meta.txt")
                    with open(meta_filepath, 'w', encoding='utf-8') as f:
                        f.write(f"Source URL: {img_url}\n")
                        f.write(f"Alt Text: {img_data.get('alt', '')}\n")
                        f.write(f"Width: {img_data.get('width', 'Unknown')}\n")
                        f.write(f"Height: {img_data.get('height', 'Unknown')}\n")
                        f.write(f"Natural Width: {img_data.get('naturalWidth', 'Unknown')}\n")
                        f.write(f"Natural Height: {img_data.get('naturalHeight', 'Unknown')}\n")
                    
                    saved_images.append(filepath)
                    if ctx:
                        await ctx.info(f"Saved image: {filepath}")
            except Exception as e:
                if ctx:
                    await ctx.warning(f"Error processing image: {str(e)}")
        
        if ctx:
            await ctx.info(f"Saved {len(saved_images)} images")
        
        # Get the text content
        text_path = os.path.join(scraper.text_dir, f"{base_filename}.txt")
        text_content = ""
        if os.path.exists(text_path):
            with open(text_path, 'r', encoding='utf-8') as f:
                text_content = f.read()
        
        # Analyze each image with Context7-enhanced analysis
        if ctx:
            await ctx.info(f"Analyzing {len(saved_images)} images with Context7-enhanced analysis...")
            await ctx.report_progress(70, 100)
        
        # Use the custom prompt if provided, otherwise use the enhanced default
        analysis_prompt = custom_prompt if custom_prompt else simple_image_analysis_prompt()
        
        image_analyses = []
        for i, image_path in enumerate(saved_images):
            if ctx:
                current_progress = 70 + int((i / max(len(saved_images), 1)) * 25)
                await ctx.report_progress(current_progress, 100)
            
            try:
                # Get image metadata for enhanced analysis
                meta_path = image_path.replace(".webp", "_meta.txt")
                meta_info = ""
                if os.path.exists(meta_path):
                    with open(meta_path, 'r', encoding='utf-8') as f:
                        meta_info = f.read()
                
                # Enhanced analysis with metadata context
                enhanced_prompt = f"""
                图片元数据信息：
                {meta_info}
                
                {analysis_prompt}
                """
                
                analysis = await asyncio.to_thread(process_image_file, image_path, enhanced_prompt)
                image_analyses.append({
                    "image_path": image_path,
                    "metadata": meta_info,
                    "analysis": analysis
                })
            except Exception as e:
                if ctx:
                    await ctx.warning(f"Failed to analyze image {image_path}: {str(e)}")
                image_analyses.append({
                    "image_path": image_path,
                    "analysis": f"Error analyzing image: {str(e)}"
                })
        
        # Create a comprehensive analysis
        if ctx:
            await ctx.info("Creating comprehensive page analysis...")
            await ctx.report_progress(95, 100)
        
        # Prepare the final result with structured data
        final_result = {
            "success": True,
            "url": url,
            "page_title": await scraper.page.title(),
            "text_content": text_content,
            "image_count": len(saved_images),
            "image_analyses": image_analyses,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if ctx:
            await ctx.info("Scraping and analysis completed!")
            await ctx.report_progress(100, 100)
        
        return final_result
    
    except Exception as e:
        if ctx:
            await ctx.error(f"Error during scrape and analyze: {str(e)}")
            await ctx.report_progress(100, 100)
        return {
            "success": False,
            "url": url,
            "error": str(e)
        }
    finally:
        if scraper:
            await scraper.close()

def is_port_in_use(port: int) -> bool:
    """Check if a port is already in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('127.0.0.1', port))
            return False
        except socket.error:
            return True

def find_process_using_port(port: int) -> int:
    """Find the process ID using the specified port."""
    try:
        if sys.platform.startswith('win'):
            # Windows
            output = subprocess.check_output(f'netstat -ano | findstr :{port}', shell=True).decode()
            if output:
                lines = output.strip().split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.strip().split()
                        return int(parts[-1])
        else:
            # Linux/Mac
            output = subprocess.check_output(f'lsof -i :{port} -t', shell=True).decode()
            if output:
                return int(output.strip())
    except (subprocess.SubprocessError, ValueError):
        pass
    return None

def kill_process(pid: int) -> bool:
    """Kill a process by its PID."""
    try:
        if sys.platform.startswith('win'):
            subprocess.check_call(f'taskkill /F /PID {pid}', shell=True)
        else:
            subprocess.check_call(f'kill -9 {pid}', shell=True)
        return True
    except subprocess.SubprocessError:
        return False

if __name__ == "__main__":
    # Check if Playwright is installed properly
    try:
        from playwright.async_api import async_playwright
        print("Playwright is properly installed.")
    except ImportError:
        print("Playwright is not installed. Please install it with: pip install playwright")
        print("After installation, run: playwright install")
        exit(1)
    
    print("Starting Web Scraper MCP with Context7-enhanced image analysis...")
    print("This service will extract text and analyze webp images from websites.")
    
    # Set up signal handlers for graceful shutdown
    def signal_handler(sig, frame):
        print("Received shutdown signal, exiting gracefully...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the server with SSE transport
    # Default host is 127.0.0.1 and port is 8000
    # You can change these by passing host and port parameters
    import asyncio
    
    async def start_server():
        print("Initializing MCP server...")
        
        # Add a longer delay to ensure proper initialization
        print("Waiting for initialization (3 seconds)...")
        await asyncio.sleep(3)
        
        # Pre-initialize any resources that might be needed
        print("Pre-initializing resources...")
        # Create output directories if they don't exist
        os.makedirs("scraped_data/images", exist_ok=True)
        os.makedirs("scraped_data/text", exist_ok=True)
        
        # Initialize a test PlaywrightScraper to ensure browser binaries are ready
        try:
            print("Testing Playwright initialization...")
            test_scraper = PlaywrightScraper()
            # Don't actually start the browser, just make sure the class can be instantiated
        except Exception as e:
            print(f"Warning: PlaywrightScraper test initialization failed: {e}")
            print("This may not affect server operation, continuing...")
        
        # Add another delay before starting the server
        await asyncio.sleep(1)
        
        # Find an available port or free up the default port
        port = 8000
        if is_port_in_use(port):
            print(f"Port {port} is already in use.")
            pid = find_process_using_port(port)
            if pid:
                print(f"Found process {pid} using port {port}. Attempting to kill it...")
                if kill_process(pid):
                    print(f"Successfully killed process {pid}")
                    # Give the OS time to release the port
                    time.sleep(1)
                else:
                    print(f"Failed to kill process {pid}")
        
        # If port is still in use, try alternative ports
        if is_port_in_use(port):
            max_port = 8010  # Try up to 10 ports
            while is_port_in_use(port) and port < max_port:
                print(f"Port {port} is still in use, trying next port...")
                port += 1
            
            if port >= max_port:
                raise RuntimeError(f"Could not find an available port between 8000 and {max_port-1}")
        
        print(f"Starting MCP server on port {port}...")
        # Use a try-except block to catch initialization errors
        try:
            # Use run_async with explicit host and port
            await mcp.run_async(
                transport="sse",
                host="127.0.0.1",
                port=port,
                # Add additional options to help with initialization
                lifespan="on",
                log_level="info",
                timeout_keep_alive=65  # Increase keep-alive timeout
            )
        except Exception as e:
            print(f"Error during MCP server startup: {str(e)}")
            raise
    
    # Use asyncio.run to properly handle the async startup
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("Server stopped by user")
    except Exception as e:
        print(f"Error starting server: {str(e)}")
        import traceback
        traceback.print_exc() 