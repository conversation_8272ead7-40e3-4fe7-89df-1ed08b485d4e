# MCP Web Scraper Service - 重构版本

这是一个重构整理后的MCP服务器，提供网页内容抓取和图片分析功能。

## 功能特性

1. **MCP服务器** - 通过SSE接口提供服务
2. **网页文本提取** - 获取网页的文本内容
3. **图片下载** - 下载网页中的图片并转换为WebP格式
4. **豆包模型解读** - 使用豆包Vision API分析图片内容

## 快速开始

### 1. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置你的豆包API密钥：
```bash
DOUBAO_API_KEY=your_actual_api_key_here
```

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install
```

### 3. 启动服务

使用简化的启动脚本：
```bash
python start_server.py
```

或者直接运行主服务：
```bash
python mcp_scraper_service.py
```

### 4. 使用服务

服务启动后，默认在 `http://127.0.0.1:8000` 提供SSE接口。

## 主要改进

### 代码结构优化
- 引入配置管理类 `Config`
- 创建图片下载器类 `ImageDownloader`
- 添加服务器管理类 `ServerManager`
- 模块化函数设计

### 错误处理增强
- 统一的日志记录
- 更好的异常处理
- 配置验证

### 性能优化
- 异步图片下载
- 智能端口分配
- 资源管理优化

## API接口

### scrape_url_analyze_content

抓取URL并分析内容的主要工具函数。

**参数：**
- `url` (str): 要抓取的网页URL
- `wait_time` (int): 等待时间（毫秒），默认15000
- `custom_prompt` (str): 自定义图片分析提示词（可选）

**返回：**
```json
{
  "success": true,
  "url": "https://example.com",
  "page_title": "页面标题",
  "text_content": "提取的文本内容",
  "image_count": 5,
  "image_analyses": [
    {
      "image_path": "path/to/image.webp",
      "metadata": "图片元数据",
      "analysis": "豆包分析结果"
    }
  ],
  "timestamp": "2024-01-01 12:00:00",
  "config_status": {
    "doubao_api_available": true,
    "output_directory": "scraped_data"
  }
}
```

## 配置选项

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `DOUBAO_API_KEY` | - | 豆包API密钥（必需） |
| `DOUBAO_MODEL_ID` | `doubao-1-5-vision-pro-32k-250115` | 豆包模型ID |
| `MCP_HOST` | `127.0.0.1` | 服务器主机 |
| `MCP_PORT` | `8000` | 服务器端口 |
| `OUTPUT_DIR` | `scraped_data` | 输出目录 |
| `MAX_WAIT_TIME` | `30000` | 最大等待时间（毫秒） |

## 输出结构

```
scraped_data/
├── images/          # 下载的图片文件
│   ├── *.webp      # 转换后的WebP图片
│   └── *_meta.txt  # 图片元数据
└── text/           # 提取的文本文件
    └── *.txt       # 网页文本内容
```

## 故障排除

### 常见问题

1. **Playwright安装问题**
   ```bash
   playwright install
   ```

2. **端口被占用**
   - 服务会自动寻找可用端口
   - 或手动设置 `MCP_PORT` 环境变量

3. **豆包API问题**
   - 检查API密钥是否正确
   - 确认网络连接正常

### 日志查看

服务使用标准Python logging，可以通过控制台查看详细日志。

## 开发说明

### 主要模块

- `mcp_scraper_service.py` - 主服务文件
- `web_scraper_playwright.py` - 网页抓取模块
- `image_analysis.py` - 图片分析模块
- `start_server.py` - 简化启动脚本

### 扩展开发

可以通过添加新的 `@mcp.tool()` 装饰器函数来扩展功能。

## 许可证

请参考项目许可证文件。
