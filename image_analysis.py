import os
import json
import base64
import logging
from io import BytesIO
from PIL import Image
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Get configuration from environment variables
DOUBAO_API_KEY = os.getenv('DOUBAO_API_KEY', "")
DOUBAO_MODEL_ID = os.getenv('DOUBAO_MODEL_ID', "doubao-1-5-vision-pro-32k-250115")

# Doubao Vision API base URL
DOUBAO_API_BASE = "https://ark.cn-beijing.volces.com/api/v3"

def analyze_image_with_doubao(image_base64, prompt="请详细分析这张图片中的内容"):
    """Send image to Doubao Vision API for analysis using OpenAI SDK"""
    try:
        logger.info(f"Analyzing image with Doubao Vision API, prompt: {prompt}")
        
        if not DOUBAO_API_KEY:
            logger.error("DOUBAO_API_KEY is not set in environment variables")
            return "Error: DOUBAO_API_KEY is not set in environment variables"
        
        # Create OpenAI client with custom base URL
        client = OpenAI(
            api_key=DOUBAO_API_KEY,
            base_url=DOUBAO_API_BASE
        )
        
        # Create the messages with text and image content
        response = client.chat.completions.create(
            model=DOUBAO_MODEL_ID,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            temperature=0.7,
            max_tokens=2000
        )
        
        logger.info(f"Successfully received response from Doubao Vision API")
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"Error analyzing image with Doubao: {e}")
        return f"图片分析失败: {str(e)}"

def process_image(image_data, prompt="请详细分析这张图片中的内容"):
    """Process image data and analyze it with Doubao Vision
    
    Args:
        image_data: Either a base64 encoded string or raw image bytes
        prompt: The prompt to send to Doubao Vision API
        
    Returns:
        The analysis result from Doubao Vision API
    """
    try:
        # Check if image_data is already a base64 string
        if isinstance(image_data, str):
            # Assume it's already base64 encoded
            image_base64 = image_data
        else:
            # Process raw image bytes - convert to jpg format
            try:
                image = Image.open(BytesIO(image_data))
                logger.info(f"Image loaded successfully, size: {image.size}, format: {image.format}")
                
                # Resize image if it's too large
                max_size = 1024  # Maximum dimension
                if max(image.size) > max_size:
                    ratio = max_size / max(image.size)
                    new_size = (int(image.size[0] * ratio), int(image.size[1] * ratio))
                    image = image.resize(new_size, Image.LANCZOS)
                    logger.info(f"Image resized to: {new_size}")
                
                # Convert to RGB if needed (for PNG with transparency, etc.)
                if image.mode != 'RGB':
                    logger.info(f"Converting image from {image.mode} to RGB")
                    image = image.convert('RGB')
                
                # Save as JPEG in memory buffer
                buffered = BytesIO()
                image.save(buffered, format="JPEG", quality=85)
                image_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                logger.info(f"Image converted to JPEG and base64, length: {len(image_base64)}")
            except Exception as e:
                logger.warning(f"Error processing image with PIL: {e}, falling back to direct base64 encoding")
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                logger.info(f"Image converted to base64, length: {len(image_base64)}")
        
        # Analyze the image
        return analyze_image_with_doubao(image_base64, prompt)
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        return f"图片处理失败: {str(e)}"

def process_image_file(image_path, prompt="请详细分析这张图片中的内容"):
    """Process an image file and analyze it with Doubao Vision
    
    Args:
        image_path: Path to the image file
        prompt: The prompt to send to Doubao Vision API
        
    Returns:
        The analysis result from Doubao Vision API
    """
    try:
        with open(image_path, "rb") as f:
            image_data = f.read()
        return process_image(image_data, prompt)
    except Exception as e:
        logger.error(f"Error reading image file: {e}")
        return f"图片文件读取失败: {str(e)}"

if __name__ == "__main__":
    # Test image processing with a sample image
    image_path = input("Enter path to image file: ")
    prompt = input("Enter prompt (default: 请详细分析这张图片中的内容): ") or "请详细分析这张图片中的内容"
    
    if os.path.exists(image_path):
        result = process_image_file(image_path, prompt)
        print(result)
    else:
        print(f"Image file not found: {image_path}") 