import os
import time
import requests
import argparse
from PIL import Image
from io import BytesIO
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager


class WebScraper:
    def __init__(self, output_dir="scraped_data"):
        """
        初始化爬虫类
        
        Args:
            output_dir: 保存爬取内容的目录
        """
        self.output_dir = output_dir
        self._setup_directories()
        self._setup_browser()
    
    def _setup_directories(self):
        """创建保存爬取内容的目录"""
        # 创建主输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 创建图片和文本子目录
        self.images_dir = os.path.join(self.output_dir, "images")
        self.text_dir = os.path.join(self.output_dir, "text")
        
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)
        if not os.path.exists(self.text_dir):
            os.makedirs(self.text_dir)
    
    def _setup_browser(self):
        """配置并启动无头Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # 使用webdriver_manager自动管理ChromeDriver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
    
    def scrape_url(self, url, wait_time=5):
        """
        爬取指定URL的内容
        
        Args:
            url: 要爬取的URL
            wait_time: 等待页面加载的时间（秒）
        """
        try:
            print(f"开始爬取: {url}")
            self.driver.get(url)
            
            # 等待页面加载完成
            time.sleep(wait_time)
            
            # 保存页面截图
            self._save_screenshot(url)
            
            # 获取并保存文本内容
            self._extract_and_save_text(url)
            
            # 获取并保存图片
            self._extract_and_save_images(url)
            
            print(f"成功爬取: {url}")
            return True
        
        except Exception as e:
            print(f"爬取 {url} 时出错: {str(e)}")
            return False
    
    def _save_screenshot(self, url):
        """保存整个页面的截图"""
        filename = self._get_filename_from_url(url) + "_screenshot.png"
        filepath = os.path.join(self.images_dir, filename)
        self.driver.save_screenshot(filepath)
        print(f"已保存页面截图: {filepath}")
    
    def _extract_and_save_text(self, url):
        """提取并保存页面文本内容"""
        # 获取页面HTML并使用BeautifulSoup解析
        html = self.driver.page_source
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除script和style元素
        for script in soup(["script", "style"]):
            script.extract()
        
        # 获取文本
        text = soup.get_text(separator='\n')
        
        # 清理文本（移除多余空行等）
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        # 保存文本
        filename = self._get_filename_from_url(url) + ".txt"
        filepath = os.path.join(self.text_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(text)
        
        print(f"已保存文本内容: {filepath}")
    
    def _extract_and_save_images(self, url):
        """提取并保存页面上的图片"""
        # 获取所有图片元素
        images = self.driver.find_elements(By.TAG_NAME, 'img')
        base_filename = self._get_filename_from_url(url)
        
        saved_count = 0
        for i, img in enumerate(images):
            try:
                # 获取图片URL
                img_url = img.get_attribute('src')
                if not img_url or img_url.startswith('data:'):
                    continue
                
                # 下载图片
                response = requests.get(img_url)
                if response.status_code == 200:
                    img_content = response.content
                    img_file = Image.open(BytesIO(img_content))
                    
                    # 保存图片
                    filename = f"{base_filename}_img_{i}.{img_file.format.lower() if img_file.format else 'jpg'}"
                    filepath = os.path.join(self.images_dir, filename)
                    img_file.save(filepath)
                    saved_count += 1
            
            except Exception as e:
                print(f"保存图片时出错: {str(e)}")
        
        print(f"已保存 {saved_count} 张图片")
    
    def _get_filename_from_url(self, url):
        """从URL生成文件名"""
        # 移除协议和www前缀
        filename = url.replace('http://', '').replace('https://', '').replace('www.', '')
        # 替换无效字符
        filename = filename.replace('/', '_').replace(':', '_').replace('?', '_').replace('&', '_')
        return filename
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()


def main():
    parser = argparse.ArgumentParser(description='网页内容爬取工具')
    parser.add_argument('url', help='要爬取的网页URL')
    parser.add_argument('--output', default='scraped_data', help='输出目录')
    parser.add_argument('--wait', type=int, default=5, help='页面加载等待时间（秒）')
    
    args = parser.parse_args()
    
    scraper = WebScraper(output_dir=args.output)
    try:
        scraper.scrape_url(args.url, wait_time=args.wait)
    finally:
        scraper.close()


if __name__ == "__main__":
    main() 