import asyncio
import json
import os
import sys
from fastmcp import Client

"""
This example demonstrates how to use Context7 with the MCP service and an LLM.
It requires the following packages:
- fastmcp
- anthropic (or openai)

To run this example, you need to set your API key:
export ANTHROPIC_API_KEY=your_api_key
"""

# Check if the API key is set
try:
    import anthropic
    if "ANTHROPIC_API_KEY" not in os.environ:
        print("Error: ANTHROPIC_API_KEY environment variable is not set.")
        print("Please set it with: export ANTHROPIC_API_KEY=your_api_key")
        sys.exit(1)
    USE_ANTHROPIC = True
except ImportError:
    try:
        import openai
        if "OPENAI_API_KEY" not in os.environ:
            print("Error: OPENAI_API_KEY environment variable is not set.")
            print("Please set it with: export OPENAI_API_KEY=your_api_key")
            sys.exit(1)
        USE_ANTHROPIC = False
    except ImportError:
        print("Error: Neither anthropic nor openai package is installed.")
        print("Please install one of them with: pip install anthropic or pip install openai")
        sys.exit(1)

async def main():
    # Connect to the MCP server using SSE transport
    mcp_client = Client("http://127.0.0.1:8000/sse")
    
    async with mcp_client:
        print("Connected to MCP server via SSE")
        
        # URL to scrape
        url = input("Enter a URL to scrape (default: https://python.org): ").strip() or "https://python.org"
        
        # First, let's scrape the URL using our MCP service
        print(f"\nScraping URL: {url}")
        
        # Create a prompt for the LLM
        user_prompt = f"""
        I want you to scrape and analyze the content from {url}.
        
        Please:
        1. Use the scrape_url_with_progress tool to scrape the website
        2. Read the scraped content using the scraped-content resource
        3. Analyze the content and provide a detailed summary
        4. Extract key information, concepts, and topics
        5. Identify any code examples or technical details
        
        Be thorough in your analysis.
        """
        
        if USE_ANTHROPIC:
            # Use Anthropic's Claude with MCP
            client = anthropic.Anthropic()
            
            try:
                print("\nSending request to Claude...")
                response = client.beta.messages.create(
                    model="claude-3-sonnet-20240229",
                    max_tokens=2000,
                    messages=[
                        {"role": "user", "content": user_prompt}
                    ],
                    mcp_servers=[mcp_client.session],
                    extra_headers={
                        "anthropic-beta": "mcp-client-2025-04-04"
                    }
                )
                
                print("\n--- Claude's Analysis ---")
                print(response.content[0].text)
                print("--- End of Analysis ---")
                
            except Exception as e:
                print(f"Error with Anthropic API: {str(e)}")
        
        else:
            # Use OpenAI with MCP
            client = openai.OpenAI()
            
            try:
                print("\nSending request to OpenAI...")
                response = client.responses.create(
                    model="gpt-4",
                    tools=[{"type": "mcp", "server": mcp_client.session}],
                    input=user_prompt
                )
                
                print("\n--- OpenAI's Analysis ---")
                print(response.output_text)
                print("--- End of Analysis ---")
                
            except Exception as e:
                print(f"Error with OpenAI API: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main()) 